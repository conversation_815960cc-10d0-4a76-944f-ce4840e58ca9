#!/bin/bash

# Simple test for ASN.1 dynamic file collection only
# This test focuses only on the ASN.1 generation and compilation

set -e

echo "=== Testing ASN.1 Dynamic File Collection Only ==="

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build_asn1_test"

echo "Project root: $PROJECT_ROOT"
echo "Build directory: $BUILD_DIR"

# Clean up completely
echo "Cleaning up completely..."
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

cd "$BUILD_DIR"

# Test 1: Create a minimal CMakeLists.txt for testing ASN.1 only
echo ""
echo "=== Test 1: Creating minimal CMake setup ==="

cat > CMakeLists.txt << EOF
cmake_minimum_required(VERSION 3.16)
project(ASN1Test)

# Set CMAKE_SOURCE_DIR to project root
set(CMAKE_SOURCE_DIR "$PROJECT_ROOT")

# Include ASN.1 functions
include(\${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/asn1_functions.cmake)

# Generate ASN.1 parser for test_simple
generate_asn1_parser(test_simple)
EOF

# Test 2: Run CMake configuration
echo ""
echo "=== Test 2: CMake configuration ==="

if cmake . -DCMAKE_BUILD_TYPE=Debug; then
    echo "✅ CMake configuration succeeded"
else
    echo "❌ CMake configuration failed"
    exit 1
fi

# Test 3: Build ASN.1 target
echo ""
echo "=== Test 3: Building ASN.1 target ==="

if make asn1_test_simple -j$(nproc); then
    echo "✅ ASN.1 target build succeeded"
else
    echo "❌ ASN.1 target build failed"
    exit 1
fi

# Test 4: Check generated files
echo ""
echo "=== Test 4: Checking generated files ==="

GENERATED_DIR="$BUILD_DIR/test/yaLiteDpi/plugins/asn1/generated/test_simple"
echo "Generated directory: $GENERATED_DIR"

if [ -d "$GENERATED_DIR" ]; then
    echo "✅ Generated directory exists"
    
    # Count C files
    C_FILE_COUNT=$(find "$GENERATED_DIR" -name "*.c" | wc -l)
    echo "✅ Found $C_FILE_COUNT C files"
    
    if [ $C_FILE_COUNT -gt 0 ]; then
        echo "✅ C files were generated successfully"
        
        # List first few files
        echo "Generated C files:"
        find "$GENERATED_DIR" -name "*.c" | head -5 | while read file; do
            echo "  - $(basename "$file")"
        done
    else
        echo "❌ No C files found"
        exit 1
    fi
else
    echo "❌ Generated directory does not exist"
    exit 1
fi

# Test 5: Check library file
echo ""
echo "=== Test 5: Checking library file ==="

LIBRARY_FILE="$BUILD_DIR/libasn1_test_simple.a"
if [ -f "$LIBRARY_FILE" ]; then
    echo "✅ Library file exists: $(basename "$LIBRARY_FILE")"
    
    # Check library size
    LIBRARY_SIZE=$(stat -c%s "$LIBRARY_FILE")
    echo "✅ Library size: $LIBRARY_SIZE bytes"
    
    if [ $LIBRARY_SIZE -gt 1000 ]; then
        echo "✅ Library has reasonable size"
        
        # Show some library contents
        echo "Library contents (first 5 objects):"
        ar -t "$LIBRARY_FILE" | head -5 | while read obj; do
            echo "  - $obj"
        done
    else
        echo "⚠️  Library seems too small"
    fi
else
    echo "❌ Library file not found: $LIBRARY_FILE"
    exit 1
fi

# Test 6: Test rebuild
echo ""
echo "=== Test 6: Testing rebuild ==="

# Touch ASN.1 file to trigger rebuild
ASN1_FILE="$PROJECT_ROOT/test/yaLiteDpi/plugins/asn1/protocols/test_simple/simple.asn"
touch "$ASN1_FILE"

echo "Rebuilding after touching ASN.1 file..."
if make asn1_test_simple -j$(nproc); then
    echo "✅ Rebuild succeeded"
else
    echo "❌ Rebuild failed"
    exit 1
fi

echo ""
echo "=== All ASN.1 Tests Completed Successfully! ==="
echo ""
echo "Summary:"
echo "- ✅ CMake configuration works without existing files"
echo "- ✅ ASN.1 files generated correctly"
echo "- ✅ C files compiled successfully"
echo "- ✅ Static library created"
echo "- ✅ Rebuild works correctly"
echo ""
echo "The ASN.1 dynamic file collection is working perfectly!"

# Clean up
echo ""
echo "Cleaning up test directory..."
cd "$PROJECT_ROOT"
rm -rf "$BUILD_DIR"
echo "✅ Cleanup completed"
