#!/bin/bash

# Test script for fresh build with dynamic ASN.1 file collection
# This script tests the fix for the "chicken and egg" problem

set -e

echo "=== Testing Fresh Build with Dynamic ASN.1 File Collection ==="

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build_test_fresh"

echo "Project root: $PROJECT_ROOT"
echo "Build directory: $BUILD_DIR"

# Clean up completely to simulate fresh build
echo "Cleaning up completely to simulate fresh build..."
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

# Test 1: Test fresh CMake configuration (should not fail)
echo ""
echo "=== Test 1: Fresh CMake configuration ==="

cd "$BUILD_DIR"

# Run CMake to configure the project
echo "Configuring CMake (this should not fail even without generated files)..."
if cmake "$PROJECT_ROOT" -DCMAKE_BUILD_TYPE=Debug; then
    echo "✅ CMake configuration succeeded"
else
    echo "❌ CMake configuration failed"
    exit 1
fi

# Test 2: Build the test_simple ASN.1 library
echo ""
echo "=== Test 2: Building ASN.1 library ==="

echo "Building asn1_test_simple library..."
if make asn1_test_simple -j$(nproc); then
    echo "✅ ASN.1 library build succeeded"
else
    echo "❌ ASN.1 library build failed"
    exit 1
fi

# Test 3: Check if files were generated
GENERATED_DIR="$BUILD_DIR/test/yaLiteDpi/plugins/asn1/generated/test_simple"
echo ""
echo "=== Test 3: Checking generated files ==="
echo "Generated directory: $GENERATED_DIR"

if [ -d "$GENERATED_DIR" ]; then
    echo "✅ Generated directory exists"
    
    # Count C files
    C_FILE_COUNT=$(find "$GENERATED_DIR" -name "*.c" | wc -l)
    echo "✅ Found $C_FILE_COUNT C files"
    
    # List some key files
    echo ""
    echo "Generated C files:"
    find "$GENERATED_DIR" -name "*.c" | head -10 | while read file; do
        echo "  - $(basename "$file")"
    done
    
    if [ $C_FILE_COUNT -gt 0 ]; then
        echo "✅ C files were generated successfully"
    else
        echo "❌ No C files found"
        exit 1
    fi
    
    # Check for discovery header
    if [ -f "$GENERATED_DIR/asn1_discovery.h" ]; then
        echo "✅ Discovery header generated"
    else
        echo "❌ Discovery header not found"
        exit 1
    fi
    
else
    echo "❌ Generated directory does not exist"
    exit 1
fi

# Test 4: Check if library was built successfully
echo ""
echo "=== Test 4: Checking library build ==="

LIBRARY_FILE="$BUILD_DIR/libasn1_test_simple.a"
if [ -f "$LIBRARY_FILE" ]; then
    echo "✅ Library file exists: $(basename "$LIBRARY_FILE")"
    
    # Check library contents
    echo "Library contents (first 10 objects):"
    ar -t "$LIBRARY_FILE" | head -10 | while read obj; do
        echo "  - $obj"
    done
    
    # Check library size
    LIBRARY_SIZE=$(stat -c%s "$LIBRARY_FILE")
    echo "✅ Library size: $LIBRARY_SIZE bytes"
    
    if [ $LIBRARY_SIZE -gt 1000 ]; then
        echo "✅ Library has reasonable size"
    else
        echo "⚠️  Library seems too small"
    fi
else
    echo "❌ Library file not found: $LIBRARY_FILE"
    exit 1
fi

# Test 5: Test incremental build
echo ""
echo "=== Test 5: Testing incremental build ==="

# Modify ASN.1 file to trigger rebuild
ASN1_FILE="$PROJECT_ROOT/test/yaLiteDpi/plugins/asn1/protocols/test_simple/simple.asn"
echo "-- Modified at $(date)" >> "$ASN1_FILE"

echo "Modified ASN.1 file, rebuilding..."
if make asn1_test_simple -j$(nproc); then
    echo "✅ Incremental build succeeded"
else
    echo "❌ Incremental build failed"
    exit 1
fi

# Check if files were regenerated
NEW_C_FILE_COUNT=$(find "$GENERATED_DIR" -name "*.c" | wc -l)
echo "✅ After rebuild: Found $NEW_C_FILE_COUNT C files"

if [ $NEW_C_FILE_COUNT -eq $C_FILE_COUNT ]; then
    echo "✅ File count consistent after rebuild"
else
    echo "⚠️  File count changed: $C_FILE_COUNT -> $NEW_C_FILE_COUNT"
fi

# Test 6: Test reconfiguration after generation
echo ""
echo "=== Test 6: Testing reconfiguration ==="

echo "Running CMake configuration again..."
if cmake "$PROJECT_ROOT" -DCMAKE_BUILD_TYPE=Debug; then
    echo "✅ Reconfiguration succeeded"
else
    echo "❌ Reconfiguration failed"
    exit 1
fi

echo ""
echo "=== All Tests Completed Successfully! ==="
echo ""
echo "Summary:"
echo "- ✅ Fresh CMake configuration works without generated files"
echo "- ✅ ASN.1 files generated dynamically"
echo "- ✅ C files collected using custom target"
echo "- ✅ Library built successfully"
echo "- ✅ Incremental build works"
echo "- ✅ Reconfiguration works"
echo ""
echo "The fresh build fix is working correctly!"

# Clean up the test ASN.1 file modification
git checkout HEAD -- "$ASN1_FILE" 2>/dev/null || true
