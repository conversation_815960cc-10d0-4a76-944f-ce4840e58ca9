-- Simple test protocol for ASN.1 dynamic file collection
SimpleProtocol DEFINITIONS ::= BEGIN

SimpleMessage ::= SEQUENCE {
    version INTEGER (1..10),
    messageType MessageType,
    data OCTET STRING (SIZE (1..1024)),
    timestamp INTEGER (0..4294967295) OPTIONAL
}

MessageType ::= INTEGER {
    request(1),
    response(2),
    notification(3),
    error(4)
}

SimpleRequest ::= SEQUENCE {
    requestId INTEGER (1..65535),
    operation Operation,
    parameters OCTET STRING OPTIONAL
}

SimpleResponse ::= SEQUENCE {
    requestId INTEGER (1..65535),
    status Status,
    result OCTET STRING OPTIONAL
}

Operation ::= INTEGER {
    get(1),
    set(2),
    delete(3),
    list(4)
}

Status ::= INTEGER {
    success(0),
    error(1),
    notFound(2),
    unauthorized(3)
}

END
