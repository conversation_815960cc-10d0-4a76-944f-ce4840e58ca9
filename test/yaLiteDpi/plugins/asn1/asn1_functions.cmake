# ASN.1 parser generation functions
# This file contains the core functions for generating ASN.1 parsers

# Helper function to collect all C files from a directory
function(collect_c_files_from_directory DIRECTORY_PATH OUTPUT_VAR)
    if(EXISTS ${DIRECTORY_PATH})
        file(GLOB C_FILES "${DIRECTORY_PATH}/*.c")
        set(${OUTPUT_VAR} ${C_FILES} PARENT_SCOPE)
    else()
        set(${OUTPUT_VAR} "" PARENT_SCOPE)
    endif()
endfunction()

# Function to generate ASN.1 parser for a specific protocol
function(generate_asn1_parser PROTOCOL_NAME)
    # Use absolute paths to work from any calling directory
    set(ASN1_SOURCE_DIR "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1")
    set(PROTOCOL_DIR "${ASN1_SOURCE_DIR}/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_BINARY_DIR}/test/yaLiteDpi/plugins/asn1/generated/${PROTOCOL_NAME}")

    # Find ASN.1 files for this protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(WARNING "No ASN.1 files found for protocol ${PROTOCOL_NAME} in ${PROTOCOL_DIR}")
        return()
    endif()

    message(STATUS "Found ASN.1 files for ${PROTOCOL_NAME}: ${ASN1_FILES}")

    # Create output directory
    file(MAKE_DIRECTORY ${GENERATED_DIR})

    # Discovery header and script paths
    set(DISCOVERY_HEADER "${GENERATED_DIR}/asn1_discovery.h")
    set(DISCOVERY_SCRIPT "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/asn1_inspector.py")

    # Create a marker file to indicate when ASN.1 generation is complete
    set(GENERATION_MARKER "${GENERATED_DIR}/.asn1_generated")

    # Custom command to generate ASN.1 files
    add_custom_command(
        OUTPUT ${GENERATION_MARKER}
        COMMAND ${CMAKE_COMMAND} -E remove_directory ${GENERATED_DIR}
        COMMAND ${CMAKE_COMMAND} -E make_directory ${GENERATED_DIR}
        COMMAND python3 ${DISCOVERY_SCRIPT} ${PROTOCOL_NAME} ${DISCOVERY_HEADER} ${ASN1_FILES}
        COMMAND asn1c -D ${GENERATED_DIR} ${ASN1_FILES}
        COMMAND ${CMAKE_COMMAND} -E touch ${GENERATION_MARKER}
        DEPENDS ${ASN1_FILES} ${DISCOVERY_SCRIPT}
        WORKING_DIRECTORY ${ASN1_SOURCE_DIR}
        COMMENT "Generating ASN.1 parser for ${PROTOCOL_NAME}"
    )

    # Create a custom target for the generation step
    set(GENERATION_TARGET "asn1_generate_${PROTOCOL_NAME}")
    add_custom_target(${GENERATION_TARGET} DEPENDS ${GENERATION_MARKER})

    # Collect all C files from the generated directory
    # Use GLOB with CONFIGURE_DEPENDS to automatically update the file list when files change
    file(GLOB GENERATED_C_FILES CONFIGURE_DEPENDS "${GENERATED_DIR}/*.c")

    # If no files exist yet (first time build), create a placeholder list
    if(NOT GENERATED_C_FILES)
        # Create a minimal list of files that asn1c typically generates
        # These will be created by the custom command above
        set(GENERATED_C_FILES
            ${GENERATED_DIR}/asn_application.c
            ${GENERATED_DIR}/asn_codecs_prim.c
            ${GENERATED_DIR}/asn_internal.c
            ${GENERATED_DIR}/ber_decoder.c
            ${GENERATED_DIR}/ber_tlv_length.c
            ${GENERATED_DIR}/ber_tlv_tag.c
            ${GENERATED_DIR}/constr_TYPE.c
            ${GENERATED_DIR}/constraints.c
            ${GENERATED_DIR}/der_encoder.c
            ${GENERATED_DIR}/per_decoder.c
            ${GENERATED_DIR}/per_encoder.c
            ${GENERATED_DIR}/per_support.c
            ${GENERATED_DIR}/xer_decoder.c
            ${GENERATED_DIR}/xer_encoder.c
            ${GENERATED_DIR}/xer_support.c
        )
        message(STATUS "Using placeholder file list for ${PROTOCOL_NAME} (first build)")
    else()
        list(LENGTH GENERATED_C_FILES FILE_COUNT)
        message(STATUS "Found ${FILE_COUNT} existing generated files for ${PROTOCOL_NAME}")
    endif()

    # Create library target with generated sources
    add_library(asn1_${PROTOCOL_NAME} STATIC ${GENERATED_C_FILES})

    # Make the library depend on the generation target
    add_dependencies(asn1_${PROTOCOL_NAME} ${GENERATION_TARGET})

    # Set include directories
    target_include_directories(asn1_${PROTOCOL_NAME} PUBLIC ${GENERATED_DIR})

    # Set compiler flags to suppress warnings from generated code
    target_compile_options(asn1_${PROTOCOL_NAME} PRIVATE
        -Wno-unused-parameter
        -Wno-unused-variable
        -Wno-unused-function
        -Wno-sign-compare
        -Wno-missing-field-initializers
        -Wno-type-limits
        -Wno-error
    )

    # Set position independent code for shared library usage
    set_target_properties(asn1_${PROTOCOL_NAME} PROPERTIES POSITION_INDEPENDENT_CODE ON)

    # Export the target name for parent scope
    set(ASN1_${PROTOCOL_NAME}_TARGET asn1_${PROTOCOL_NAME} PARENT_SCOPE)
endfunction()

# Function to generate ASN.1 structure discovery header
function(generate_asn1_discovery_header PROTOCOL_NAME)
    set(PROTOCOL_DIR "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_CURRENT_BINARY_DIR}/asn1/generated/${PROTOCOL_NAME}")
    set(DISCOVERY_HEADER "${GENERATED_DIR}/asn1_discovery.h")

    # Find all ASN.1 files for the protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(FATAL_ERROR "No ASN.1 files found in ${PROTOCOL_DIR}")
    endif()

    # Use the existing asn1_inspector.py script for discovery header generation
    set(DISCOVERY_SCRIPT "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/asn1_inspector.py")

    # Make script executable
    execute_process(COMMAND chmod +x ${DISCOVERY_SCRIPT})

    # Generate discovery header
    add_custom_command(
        OUTPUT ${DISCOVERY_HEADER}
        COMMAND python3 ${DISCOVERY_SCRIPT} ${PROTOCOL_NAME} ${DISCOVERY_HEADER} ${ASN1_FILES}
        DEPENDS ${ASN1_FILES} ${DISCOVERY_SCRIPT}
        COMMENT "Generating ASN.1 discovery header for ${PROTOCOL_NAME}"
    )

    # Add to generated files
    set(DISCOVERY_TARGET_NAME asn1_discovery_${PROTOCOL_NAME})
    add_custom_target(${DISCOVERY_TARGET_NAME} DEPENDS ${DISCOVERY_HEADER})

    # Export for parent scope
    set(ASN1_DISCOVERY_HEADER_${PROTOCOL_NAME} ${DISCOVERY_HEADER} PARENT_SCOPE)
    set(ASN1_DISCOVERY_TARGET_${PROTOCOL_NAME} ${DISCOVERY_TARGET_NAME} PARENT_SCOPE)
endfunction()
