# ASN.1协议开发完整指南

## 📖 目录

1. [概述](#概述)
2. [快速开始](#快速开始)
3. [详细开发指南](#详细开发指南)
4. [实际示例：LDAP协议](#实际示例ldap协议)
5. [快速参考](#快速参考)
6. [故障排除](#故障排除)
7. [最佳实践](#最佳实践)

---

## 概述

### 问题描述

在开发新的ASN.1协议时，开发者面临的主要挑战：
1. 不知道asn1c生成的结构体名称（如 `Message_t`, `PDU_t`）
2. 不知道类型描述符名称（如 `asn_DEF_Message`）
3. 不知道枚举类型名称（如 `PDUs_PR`）

### 解决方案架构

本框架提供完整的ASN.1协议开发工具链：

```text
ASN.1定义文件 → 结构预测工具 → 自动代码生成 → 发现头文件 → 协议解析器
```

### 核心组件

1. **ASN.1结构发现工具** (`asn1_inspector.py`)
2. **自动生成的发现头文件** (`asn1_discovery.h`)
3. **集成的构建系统** (CMake集成)
4. **完整的文档体系**

---

## 快速开始

### 🚀 3步添加新协议

```bash
# 1. 创建协议目录
mkdir test/yaLiteDpi/plugins/asn1/protocols/YOUR_PROTOCOL

# 2. 添加ASN.1文件
cp your_protocol.asn test/yaLiteDpi/plugins/asn1/protocols/YOUR_PROTOCOL/

# 3. 注册协议
echo "YOUR_PROTOCOL" >> test/yaLiteDpi/plugins/asn1/protocols.cmake
```

### 🔍 预览生成的结构

```bash
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py your_protocol.asn
```

### 🔨 构建并获取发现头文件

```bash
make -C build
# 发现头文件位置：
# build/test/yaLiteDpi/plugins/asn1/generated/YOUR_PROTOCOL/asn1_discovery.h
```

---

## 详细开发指南

### ASN.1结构命名规则

asn1c工具遵循以下命名约定：

#### 基本规则
- **ASN.1定义**: `TypeName ::= SEQUENCE { ... }`
- **生成的结构体**: `TypeName_t`
- **类型描述符**: `asn_DEF_TypeName`
- **CHOICE枚举**: `TypeName_PR` (Present)

#### 特殊转换
- 连字符转下划线: `Trap-PDU` → `Trap_PDU_t`
- 头文件名保持原样: `Trap-PDU.h`
- 保持大小写: `Message` → `Message_t`

### 自动发现工具

框架提供自动生成的发现头文件：`asn1_discovery.h`

#### 使用方法
```c
#include "asn1_discovery.h"  // 包含所有ASN.1结构定义和使用示例
```

#### 发现头文件内容
- 所有生成的头文件包含
- 结构体名称映射
- 类型描述符列表
- 使用示例代码

### 开发新协议的详细步骤

#### 步骤1: 准备ASN.1定义文件
```bash
# 创建协议目录
mkdir test/yaLiteDpi/plugins/asn1/protocols/your_protocol

# 添加ASN.1定义文件
cp your_protocol.asn test/yaLiteDpi/plugins/asn1/protocols/your_protocol/
```

#### 步骤2: 注册协议
编辑 `test/yaLiteDpi/plugins/asn1/protocols.cmake`:
```cmake
set(ASN1_PROTOCOLS
    snmp
    your_protocol  # 添加新协议
)
```

#### 步骤3: 构建并查看生成的结构
```bash
cd build
make
```

查看生成的发现头文件：
```bash
cat build/test/yaLiteDpi/plugins/asn1/generated/your_protocol/asn1_discovery.h
```

#### 步骤4: 实现解析器
创建 `dissector_your_protocol.c`:
```c
#include "asn1_discovery.h"  // 包含所有ASN.1定义

int your_protocol_dissect(/* parameters */) {
    // 使用发现头文件中的示例代码
    YourMessage_t *message = NULL;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_YourMessage,
                                       (void **)&message, data, data_len);
    
    if (result.code != RC_OK) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }
    
    // 处理解析后的数据
    // ...
    
    ASN_STRUCT_FREE(asn_DEF_YourMessage, message);
    return NXT_DISSECT_ST_OK;
}
```

### 调试和验证

#### 查看生成的文件
```bash
# 查看所有生成的头文件
ls build/test/yaLiteDpi/plugins/asn1/generated/your_protocol/*.h

# 查看主要结构定义
cat build/test/yaLiteDpi/plugins/asn1/generated/your_protocol/YourMessage.h
```

#### 使用调试功能
```c
// 打印ASN.1结构（调试用）
asn_fprint(stdout, &asn_DEF_YourMessage, message);

// 验证结构完整性
int check_result = asn_check_constraints(&asn_DEF_YourMessage, message, 
                                         error_buffer, &error_size);
```

### 常见ASN.1模式

#### SEQUENCE类型
```asn1
Message ::= SEQUENCE {
    version INTEGER,
    data OCTET STRING
}
```
生成：`Message_t`, `asn_DEF_Message`

#### CHOICE类型
```asn1
PDUs ::= CHOICE {
    request [0] IMPLICIT PDU,
    response [1] IMPLICIT PDU
}
```
生成：`PDUs_t`, `PDUs_PR`, `asn_DEF_PDUs`

#### 应用标签
```asn1
Counter ::= [APPLICATION 1] IMPLICIT INTEGER (0..4294967295)
```
生成：`Counter_t`, `asn_DEF_Counter`

---

## 实际示例：LDAP协议

### 场景描述

假设我们要为yaEngineNext添加LDAP协议支持。

### 步骤1: 准备ASN.1定义文件

创建 `test/yaLiteDpi/plugins/asn1/protocols/ldap/ldap.asn`:

```asn1
LDAP-Protocol DEFINITIONS ::= BEGIN

LDAPMessage ::= SEQUENCE {
    messageID MessageID,
    protocolOp CHOICE {
        bindRequest [0] BindRequest,
        bindResponse [1] BindResponse,
        unbindRequest [2] UnbindRequest,
        searchRequest [3] SearchRequest,
        searchResEntry [4] SearchResultEntry,
        searchResDone [5] SearchResultDone
    },
    controls [0] Controls OPTIONAL
}

MessageID ::= INTEGER (0..maxInt)

BindRequest ::= [APPLICATION 0] SEQUENCE {
    version INTEGER (1..127),
    name LDAPDN,
    authentication CHOICE {
        simple [0] OCTET STRING,
        sasl [3] SaslCredentials
    }
}

BindResponse ::= [APPLICATION 1] SEQUENCE {
    COMPONENTS OF LDAPResult,
    serverSaslCreds [7] OCTET STRING OPTIONAL
}

LDAPDN ::= LDAPString
LDAPString ::= OCTET STRING

maxInt INTEGER ::= 2147483647

END
```

### 步骤2: 使用发现工具预览结构

```bash
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py \
    test/yaLiteDpi/plugins/asn1/protocols/ldap/ldap.asn
```

输出示例：
```
============================================================
ASN.1 结构预测报告
============================================================

发现 6 个类型定义:

1. LDAPMessage
   C结构体名称: LDAPMessage_t
   类型描述符: asn_DEF_LDAPMessage
   类型: SEQUENCE

2. MessageID
   C结构体名称: MessageID_t
   类型描述符: asn_DEF_MessageID
   类型: INTEGER
...
```

### 步骤3: 实现解析器

创建 `test/yaLiteDpi/plugins/dissector_ldap.c`:

```c
#include <yaEngineNext.h>
#include <yaLiteDpi.h>

// 包含ASN.1发现头文件 - 这里包含了所有需要的结构定义
#include "asn1_discovery.h"

#define PROTO_NAME "ldap"

static int ldap_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf) {
    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // 获取原始数据
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // 使用发现头文件中的结构进行解码
    LDAPMessage_t *ldap_message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_LDAPMessage, 
                              (void **)&ldap_message, data, data_len);

    if (decode_result.code != RC_OK) {
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // 提取字段 - 使用发现头文件中的结构信息
    precord_put(precord, "message_id", uinteger, ldap_message->messageID);

    // 处理协议操作 - 使用发现头文件中的枚举信息
    switch (ldap_message->protocolOp.present) {
        case protocolOp_PR_bindRequest:
            precord_put(precord, "operation", string, "BindRequest");
            break;
        case protocolOp_PR_bindResponse:
            precord_put(precord, "operation", string, "BindResponse");
            break;
        default:
            precord_put(precord, "operation", string, "Unknown");
            break;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // 释放解析的消息 - 使用发现头文件中的描述符
    ASN_STRUCT_FREE(asn_DEF_LDAPMessage, ldap_message);

    nxt_session_destroy_record(engine, session, precord);
    return NXT_DISSECT_ST_OK;
}

// 注册解析器
static nxt_dissector_t ldap_dissector = {
    .name = PROTO_NAME,
    .dissect = ldap_dissect,
};

NXT_DISSECTOR_REGISTER(ldap_dissector);
```

---

## 快速参考

### 📋 ASN.1命名规则

| ASN.1定义 | 生成的C结构 | 类型描述符 | 枚举（CHOICE） |
|-----------|-------------|------------|----------------|
| `Message ::= SEQUENCE` | `Message_t` | `asn_DEF_Message` | - |
| `PDUs ::= CHOICE` | `PDUs_t` | `asn_DEF_PDUs` | `PDUs_PR` |
| `Trap-PDU ::= SEQUENCE` | `Trap_PDU_t` | `asn_DEF_Trap_PDU` | - |

### 🔧 解析器模板

```c
#include "asn1_discovery.h"  // 包含所有ASN.1结构

static int your_protocol_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf) {
    // 1. 创建记录
    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, "your_protocol");

    // 2. 获取数据
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // 3. 解码ASN.1（使用发现头文件中的结构）
    YourMessage_t *message = NULL;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_YourMessage,
                                       (void **)&message, data, data_len);

    if (result.code != RC_OK) {
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // 4. 提取字段
    precord_put(precord, "field_name", uinteger, message->field_value);

    // 5. 处理CHOICE类型
    switch (message->choice_field.present) {
        case YourChoice_PR_option1:
            // 处理选项1
            break;
        case YourChoice_PR_option2:
            // 处理选项2
            break;
    }

    // 6. 发送事件
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // 7. 清理
    ASN_STRUCT_FREE(asn_DEF_YourMessage, message);
    nxt_session_destroy_record(engine, session, precord);
    return NXT_DISSECT_ST_OK;
}
```

### 🛠️ 常用工具命令

#### 检查ASN.1结构
```bash
# 详细分析
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py protocol.asn

# 生成头文件
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py protocol_name output.h protocol.asn
```

#### 构建特定协议
```bash
# 构建所有
make -C build

# 构建特定ASN.1库
make -C build asn1_YOUR_PROTOCOL

# 生成发现头文件
make -C build asn1_discovery_YOUR_PROTOCOL
```

#### 调试ASN.1解析
```c
// 打印解析结果
asn_fprint(stdout, &asn_DEF_YourMessage, message);

// 验证约束
char error_buffer[256];
size_t error_size = sizeof(error_buffer);
int check_result = asn_check_constraints(&asn_DEF_YourMessage, message, 
                                         error_buffer, &error_size);
```

---

## 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 未定义符号错误 | 检查协议是否在 `ASN1_PROTOCOLS` 列表中 |
| 解析失败 | 验证ASN.1语法，检查标签冲突 |
| 编译错误 | 确保包含了 `asn1_discovery.h` |
| 内存泄漏 | 确保调用了 `ASN_STRUCT_FREE()` |
| 找不到结构 | 使用 `asn1_inspector.py` 检查生成的结构名称 |

### 调试技巧

1. **使用结构预测工具**：
```bash
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py your_file.asn
```

2. **检查生成的文件**：
```bash
ls build/test/yaLiteDpi/plugins/asn1/generated/your_protocol/
```

3. **验证头文件包含**：
```bash
grep -n "include" build/test/yaLiteDpi/plugins/asn1/generated/your_protocol/asn1_discovery.h
```

4. **运行验证脚本**：
```bash
./test/yaLiteDpi/plugins/asn1/verify_solution.sh
```

---

## 最佳实践

### 错误处理
```c
Message_t *message = NULL;
asn_dec_rval_t result = ber_decode(0, &asn_DEF_Message, 
                                   (void **)&message, data, data_len);

if (result.code != RC_OK) {
    // 解码失败，message可能为NULL或部分解析
    if (message) {
        ASN_STRUCT_FREE(asn_DEF_Message, message);
    }
    return error_code;
}

// 使用message...

// 总是释放内存
ASN_STRUCT_FREE(asn_DEF_Message, message);
```

### 内存管理
- 总是使用 `ASN_STRUCT_FREE()` 释放解析的结构
- 检查解码结果的 `result.code`
- 处理部分解析的情况

### 性能优化
- 重用解码缓冲区
- 避免不必要的结构复制
- 使用适当的解码器（BER/DER/PER）

### 开发流程
1. 先使用 `asn1_inspector.py` 预览结构
2. 创建简单的测试用例
3. 逐步实现完整的解析逻辑
4. 使用调试工具验证结果

---

## 🎯 核心优势

✅ **无需猜测结构名称** - 发现头文件提供完整映射  
✅ **标准化开发流程** - 一致的协议添加过程  
✅ **自动化构建** - CMake自动处理ASN.1代码生成  
✅ **调试友好** - 丰富的调试和验证工具  
✅ **文档完整** - 包含使用示例和最佳实践  

---

## 📞 获取帮助

1. **运行验证脚本**: `./test/yaLiteDpi/plugins/asn1/verify_solution.sh`
2. **查看现有实现**: 学习 `dissector_snmp.c`
3. **工具帮助**: `python3 asn1_inspector.py --help`

---

💡 **提示**: 始终先使用 `asn1_inspector.py` 预览结构，再开始编写解析器代码！
